# Express TypeScript Backend

This is a simple Express backend project written in TypeScript. It provides basic CRUD operations for users and reads initial data from a JSON file.

## Requirements

- Node.js (v18 or newer recommended)
- npm (v9 or newer recommended)

## Dependencies

- express
- body-parser
- dotenv

## Dev Dependencies

- typescript
- ts-node
- nodemon
- eslint
- prettier
- @types/express
- @types/node

## Project Structure

```
backend/
├── json/
│   └── data.json
├── src/
│   └── server.ts
├── package.json
├── tsconfig.json
```

## Setup Instructions

1. **Clone the repository**

   ```sh
   git clone <your-repo-url>
   cd backend
   ```

2. **Install dependencies**

   ```sh
   npm install
   ```

3. **Configure environment variables**
   - Create a `.env` file in the root directory (optional).
   - Example:
     ```env
     PORT=2000
     ```

4. **Build the project**
   ```sh
   npm run build
   ```

## Running the Project

- **Development mode (with auto-reload):**

  ```sh
  npm run dev
  ```

- **Production mode:**
  ```sh
  npm run build
  npm start
  ```

## Linting

To check code style and errors:

```sh
npm run lint
```

## API Endpoints

## Custom Utilities Documentation

### Custom Response Handler

Use the `ResponseHandler` class to standardize API responses. It ensures all responses have a consistent structure for both success and error cases. For consistency, use the provided constants: `SUCCESS_MESSAGE`, `ERROR_MESSAGE`, and `ERROR_DETAILS` from your utils.

**Success Example:**

```typescript
import { ResponseHandler, SUCCESS_MESSAGE } from "@/utils/response-handler";

// In a controller:
res
  .status(200)
  .json(
    ResponseHandler.success(SUCCESS_MESSAGE.OPERATION_SUCCESSFUL, {
      foo: "bar",
    }),
  );
```

**Error Example:**

```typescript
import { ResponseHandler } from "@/utils/response-handler";
import { ERROR_MESSAGE, ERROR_DETAILS } from "@/utils/custom-error";

// In a controller or error handler:
res
  .status(400)
  .json(
    ResponseHandler.error(ERROR_MESSAGE.BAD_REQUEST, ERROR_DETAILS.BAD_REQUEST),
  );
```

### Global Error Handler

The global error handler middleware automatically formats errors using `ResponseHandler.error`. To use it, just throw or pass errors to `next(error)` in your controllers:

```typescript
import { CustomError } from "@/utils/custom-error";

// In a controller:
if (!user) {
   throw new CustomError(404, "Not Found", "User not found");
}
// Or in a catch block:
catch (error) {
   next(error);
}
```

The error handler will respond with a JSON error object.

### Custom JSON Handler

Use the `JSONHandler` utility to read and write JSON files asynchronously. This is useful for working with data files in your project.

**Read Example:**

```typescript
import { JSONHandler } from "@/utils/json-handler";

const data = await JSONHandler.read("../../json/data.json");
console.log(data);
```

**Write Example:**

```typescript
import { JSONHandler } from "@/utils/json-handler";

await JSONHandler.write("../../json/data.json", { users: [] });
```

## Notes

- The server uses a JSON file for persistent user data.
- Logging middleware is enabled for all requests.
- You can customize the port using the `PORT` environment variable.

---

Author: purwadhika
License: ISC

- You can customize the port using the `PORT` environment variable.

---

Author: purwadhika
License: ISC
