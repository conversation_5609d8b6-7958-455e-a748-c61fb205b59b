generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  uid       String   @unique @default(uuid())
  username  String   @unique
  email     String   @unique
  password  String
  salt      String
  verified  <PERSON>olean  @default(false)
  active    Boolean  @default(false)
  role      String   @default("user")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Article {
  id        Int       @id @default(autoincrement())
  title     String
  content   String
  published Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}